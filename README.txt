=== Recently Viewed Product for WooCommerce ===

Contributors: maheshpatel
Tags: woocommerce,  recently viewed product, online store, shopping cart, e-commerce
Requires at least: 5.0
Tested up to: 6.7.1
Requires PHP: 7.4
Stable tag: 2.0.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Recently Viewed Products for WooCommerce Listing page, you can easily add recently viewed product section by activate the plugin.

== Description ==

Display recently browsed items to jog your customers' memory regarding their previous interests. Eliminate the need for customers to redo their searches, leading to an uptick in sales. You can manage plugin title, display list of product and layout of product from admin.

Maximize Sales and Convenience with Recently Viewed Product for WooCommerce

Don't let your customers lose sight of what caught their eye! Introducing our "Recently Viewed Product for WooCommerce" feature, designed to enhance their shopping experience and boost your sales.

Why "Recently Viewed Product for WooCommerce" Matter:
Convenience Redefined: Save your customers time and effort by displaying items they've recently browsed. No more repeating searches or losing track of favorite finds.

Personalized Shopping: Showcasing previously viewed products demonstrates that you value your customers' preferences. It adds a personalized touch to their journey, enhancing their overall satisfaction.

Increase Sales: By keeping desired items in plain sight, you significantly increase the likelihood of conversion. Reminding customers of products they've shown interest in nudges them closer to making a purchase.

How It Works:
Seamlessly integrated into your website or app interface.
Customizable display options to match your brand's aesthetic.
Compatible across devices for a consistent shopping experience.
The Bottom Line:
Empower your customers with the convenience they crave while driving up your sales figures. With "Recently Viewed Product for WooCommerce," you not only streamline the shopping process but also foster a deeper connection with your audience.

Ready to revolutionize your customers' shopping journey? Let's get started!

== Installation ==

= Installation from within WordPress =
Simply Install the "Recently Viewed Product for WooCommerce" plugin and active it from plugin directory page. After plugin with start that work and plugin dislay recently viewed product section in product detail page. 

1. Visit **Plugins > Add New**.
2. Search for **Recently Viewed Product for WooCommerce**.
3. Install and activate the Recently Viewed Product for WooCommerce plugin.

= Manual installation =

1. Upload the entire `recently-viewed-products-for-woocommerce` folder to the `/wp-content/plugins/` directory.
2. Visit **Plugins**.
3. Activate the Recently Viewed Product for WooCommerce plugin.

== Frequently Asked Questions ==

= Waht is Recently Viewed Product for WooCommerce Plugin? =

Recently Viewed Product for WooCommerce Plugin provide functionality to display most recent product under product detail.

= How we can manage section title? =

You can manage it from admin under Settings > Recently viewd Products.

= I want to display 10 product in recently viewed product section? =

Yes, you can manage it from Settings > Recently viewd Products.


== Changelog ==

= 2.0.0 =

* Create Initial Plugin.
* Release version of  2.0.0.
